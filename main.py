#!/usr/bin/env python3
"""
Interactive Prompt Playground - Main Application
Test OpenAI models with different parameters for product descriptions
"""

import sys
import argparse
from typing import List
import config
from playground import PromptPlayground
import utils
from colorama import Fore, Style, init

# Initialize colorama
init(autoreset=True)

def get_user_input_products() -> List[str]:
    """Get product list from user input."""
    print(f"\n{Fore.CYAN}Enter products to test (one per line, empty line to finish):")
    print(f"{Fore.YELLOW}Default products will be used if none provided.")
    
    products = []
    while True:
        product = input(f"{Fore.GREEN}Product: ").strip()
        if not product:
            break
        products.append(product)
    
    return products if products else config.DEFAULT_PRODUCTS

def get_user_input_prompts() -> tuple:
    """Get custom prompts from user input."""
    print(f"\n{Fore.CYAN}=== PROMPT CONFIGURATION ===")
    
    print(f"\n{Fore.YELLOW}Current system prompt:")
    print(f"{Fore.WHITE}{config.DEFAULT_SYSTEM_PROMPT}")
    
    use_custom_system = input(f"\n{Fore.GREEN}Use custom system prompt? (y/N): ").lower().startswith('y')
    
    if use_custom_system:
        print(f"{Fore.CYAN}Enter your system prompt:")
        system_prompt = input().strip()
        if not system_prompt:
            system_prompt = config.DEFAULT_SYSTEM_PROMPT
    else:
        system_prompt = config.DEFAULT_SYSTEM_PROMPT
    
    print(f"\n{Fore.YELLOW}Current user prompt template:")
    print(f"{Fore.WHITE}{config.DEFAULT_USER_PROMPT}")
    print(f"{Fore.YELLOW}Note: Use {{product}} as placeholder for product name")
    
    use_custom_user = input(f"\n{Fore.GREEN}Use custom user prompt? (y/N): ").lower().startswith('y')
    
    if use_custom_user:
        print(f"{Fore.CYAN}Enter your user prompt template:")
        user_prompt = input().strip()
        if not user_prompt:
            user_prompt = config.DEFAULT_USER_PROMPT
    else:
        user_prompt = config.DEFAULT_USER_PROMPT
    
    return system_prompt, user_prompt

def get_user_input_models() -> List[str]:
    """Get model selection from user input."""
    print(f"\n{Fore.CYAN}=== MODEL SELECTION ===")
    print(f"{Fore.YELLOW}Available models:")
    for i, model in enumerate(config.MODELS, 1):
        print(f"{Fore.WHITE}{i}. {model}")

    print(f"\n{Fore.GREEN}Select models to test:")
    print(f"{Fore.YELLOW}Enter numbers separated by commas (e.g., 1,2) or press Enter for all models")

    selection = input(f"{Fore.GREEN}Selection: ").strip()

    if not selection:
        return config.MODELS

    if selection.lower() == 'all':
        return config.MODELS

    try:
        indices = [int(x.strip()) - 1 for x in selection.split(',')]
        selected_models = [config.MODELS[i] for i in indices if 0 <= i < len(config.MODELS)]
        if selected_models:
            return selected_models
        else:
            print(f"{Fore.RED}Invalid selection. Using all models.")
            return config.MODELS
    except (ValueError, IndexError):
        print(f"{Fore.RED}Invalid selection. Using all models.")
        return config.MODELS

def get_user_input_parameters() -> dict:
    """Get custom parameter values from user input."""
    print(f"\n{Fore.CYAN}=== PARAMETER CUSTOMIZATION ===")
    print(f"{Fore.YELLOW}Current parameter values:")
    for param, values in config.TEST_PARAMETERS.items():
        print(f"{Fore.WHITE}{param}: {values}")

    print(f"\n{Fore.GREEN}Do you want to customize parameters? (y/N): ", end="")
    customize = input().strip().lower()

    if not customize.startswith('y'):
        return config.TEST_PARAMETERS

    custom_params = {}

    # Temperature
    print(f"\n{Fore.CYAN}Temperature values (controls creativity, 0.0-2.0):")
    print(f"{Fore.YELLOW}Current: {config.TEST_PARAMETERS['temperature']}")
    temp_input = input(f"{Fore.GREEN}Enter temperature values (comma-separated) or press Enter to keep current: ").strip()
    if temp_input:
        try:
            custom_params['temperature'] = [float(x.strip()) for x in temp_input.split(',')]
        except ValueError:
            print(f"{Fore.RED}Invalid input. Using default values.")
            custom_params['temperature'] = config.TEST_PARAMETERS['temperature']
    else:
        custom_params['temperature'] = config.TEST_PARAMETERS['temperature']

    # Max Tokens
    print(f"\n{Fore.CYAN}Max tokens (response length, 1-4000):")
    print(f"{Fore.YELLOW}Current: {config.TEST_PARAMETERS['max_tokens']}")
    tokens_input = input(f"{Fore.GREEN}Enter max token values (comma-separated) or press Enter to keep current: ").strip()
    if tokens_input:
        try:
            custom_params['max_tokens'] = [int(x.strip()) for x in tokens_input.split(',')]
        except ValueError:
            print(f"{Fore.RED}Invalid input. Using default values.")
            custom_params['max_tokens'] = config.TEST_PARAMETERS['max_tokens']
    else:
        custom_params['max_tokens'] = config.TEST_PARAMETERS['max_tokens']

    # Presence Penalty
    print(f"\n{Fore.CYAN}Presence penalty (encourages new topics, -2.0 to 2.0):")
    print(f"{Fore.YELLOW}Current: {config.TEST_PARAMETERS['presence_penalty']}")
    presence_input = input(f"{Fore.GREEN}Enter presence penalty values (comma-separated) or press Enter to keep current: ").strip()
    if presence_input:
        try:
            custom_params['presence_penalty'] = [float(x.strip()) for x in presence_input.split(',')]
        except ValueError:
            print(f"{Fore.RED}Invalid input. Using default values.")
            custom_params['presence_penalty'] = config.TEST_PARAMETERS['presence_penalty']
    else:
        custom_params['presence_penalty'] = config.TEST_PARAMETERS['presence_penalty']

    # Frequency Penalty
    print(f"\n{Fore.CYAN}Frequency penalty (reduces repetition, -2.0 to 2.0):")
    print(f"{Fore.YELLOW}Current: {config.TEST_PARAMETERS['frequency_penalty']}")
    freq_input = input(f"{Fore.GREEN}Enter frequency penalty values (comma-separated) or press Enter to keep current: ").strip()
    if freq_input:
        try:
            custom_params['frequency_penalty'] = [float(x.strip()) for x in freq_input.split(',')]
        except ValueError:
            print(f"{Fore.RED}Invalid input. Using default values.")
            custom_params['frequency_penalty'] = config.TEST_PARAMETERS['frequency_penalty']
    else:
        custom_params['frequency_penalty'] = config.TEST_PARAMETERS['frequency_penalty']

    return custom_params

def display_test_configuration(models: List[str], products: List[str],
                             system_prompt: str, user_prompt: str, parameters: dict) -> None:
    """Display the test configuration before running."""
    print(f"\n{Fore.CYAN}{'='*60}")
    print(f"{Fore.YELLOW}TEST CONFIGURATION")
    print(f"{Fore.CYAN}{'='*60}")

    print(f"{Fore.GREEN}Models: {Fore.WHITE}{', '.join(models)}")
    print(f"{Fore.GREEN}Products: {Fore.WHITE}{len(products)} items")
    for product in products:
        print(f"  {Fore.WHITE}- {product}")

    print(f"\n{Fore.GREEN}Parameters to test:")
    for param, values in parameters.items():
        print(f"  {Fore.WHITE}{param}: {values}")

    total_combinations = 1
    for values in parameters.values():
        total_combinations *= len(values)

    total_tests = len(models) * len(products) * total_combinations
    print(f"\n{Fore.YELLOW}Total tests: {Fore.WHITE}{total_tests}")

    # Estimate time and cost
    avg_time_per_test = 2.5  # seconds
    estimated_time = total_tests * avg_time_per_test
    print(f"{Fore.YELLOW}Estimated time: {Fore.WHITE}{estimated_time/60:.1f} minutes")

    if 'gpt-4' in models:
        print(f"{Fore.YELLOW}⚠️  Note: GPT-4 tests will incur higher API costs")

    print(f"{Fore.CYAN}{'='*60}")

def main():
    """Main application entry point."""
    parser = argparse.ArgumentParser(description='Interactive Prompt Playground')
    parser.add_argument('--auto', action='store_true', 
                       help='Run with default settings without user input')
    parser.add_argument('--models', nargs='+', choices=config.MODELS,
                       help='Specify models to test')
    parser.add_argument('--products', nargs='+',
                       help='Specify products to test')
    parser.add_argument('--detailed', action='store_true',
                       help='Show detailed results')
    
    args = parser.parse_args()
    
    print(f"{Fore.CYAN}{'='*60}")
    print(f"{Fore.YELLOW}🚀 INTERACTIVE PROMPT PLAYGROUND 🚀")
    print(f"{Fore.CYAN}{'='*60}")
    
    # Initialize playground
    try:
        playground = PromptPlayground(config.OPENAI_API_KEY)
        print(f"{Fore.GREEN}✓ OpenAI client initialized successfully")
    except Exception as e:
        print(f"{Fore.RED}❌ Failed to initialize OpenAI client: {e}")
        sys.exit(1)
    
    # Get configuration
    if args.auto:
        models = args.models or config.MODELS
        products = args.products or config.DEFAULT_PRODUCTS
        system_prompt = config.DEFAULT_SYSTEM_PROMPT
        user_prompt = config.DEFAULT_USER_PROMPT
        parameters = config.TEST_PARAMETERS
    else:
        models = get_user_input_models()
        products = get_user_input_products()
        system_prompt, user_prompt = get_user_input_prompts()
        parameters = get_user_input_parameters()

    # Display configuration
    display_test_configuration(models, products, system_prompt, user_prompt, parameters)
    
    if not args.auto:
        confirm = input(f"\n{Fore.GREEN}Proceed with testing? (Y/n): ").lower()
        if confirm.startswith('n'):
            print(f"{Fore.YELLOW}Testing cancelled.")
            sys.exit(0)
    
    # Run tests
    try:
        # Update playground with custom parameters
        if not args.auto:
            # Temporarily update config for this run
            original_params = config.TEST_PARAMETERS.copy()
            config.TEST_PARAMETERS = parameters

        print(f"\n{Fore.CYAN}Starting comprehensive testing...")
        results = playground.run_comprehensive_test(models, products, system_prompt, user_prompt)

        # Restore original parameters if modified
        if not args.auto:
            config.TEST_PARAMETERS = original_params
        
        # Display results
        playground.display_results(detailed=args.detailed)
        
        # Save results
        playground.save_results()
        
        # Display analysis summary
        summary = playground.get_analysis_summary()
        print(f"\n{Fore.CYAN}=== ANALYSIS SUMMARY ===")
        print(f"{Fore.GREEN}Total Tests: {Fore.WHITE}{summary['total_tests']}")
        print(f"{Fore.GREEN}Successful: {Fore.WHITE}{summary['successful_tests']}")
        print(f"{Fore.GREEN}Failed: {Fore.WHITE}{summary['failed_tests']}")
        print(f"{Fore.GREEN}Average Response Time: {Fore.WHITE}{summary['avg_response_time']:.2f}s")
        
        print(f"\n{Fore.YELLOW}✨ Testing completed successfully!")
        print(f"{Fore.CYAN}Results saved to 'results' directory")
        
    except KeyboardInterrupt:
        print(f"\n{Fore.YELLOW}Testing interrupted by user.")
        sys.exit(0)
    except Exception as e:
        print(f"\n{Fore.RED}❌ Error during testing: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()

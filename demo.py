#!/usr/bin/env python3
"""
Quick demo script for the Interactive Prompt Playground
Runs a small subset of tests to demonstrate functionality
"""

import config
from playground import PromptPlayground
import utils
from colorama import Fore, init

# Initialize colorama
init(autoreset=True)

def run_demo():
    """Run a quick demo with limited parameters."""
    print(f"{Fore.CYAN}{'='*60}")
    print(f"{Fore.YELLOW}🎮 INTERACTIVE PROMPT PLAYGROUND - DEMO")
    print(f"{Fore.CYAN}{'='*60}")
    
    # Initialize playground
    try:
        playground = PromptPlayground(config.OPENAI_API_KEY)
        print(f"{Fore.GREEN}✓ OpenAI client initialized successfully")
    except Exception as e:
        print(f"{Fore.RED}❌ Failed to initialize OpenAI client: {e}")
        return
    
    # Demo configuration - limited parameters for quick testing
    demo_models = ['gpt-3.5-turbo']
    demo_products = ['iPhone 15 Pro']
    demo_system_prompt = config.DEFAULT_SYSTEM_PROMPT
    demo_user_prompt = config.DEFAULT_USER_PROMPT
    
    # Override test parameters for demo (smaller subset)
    original_params = config.TEST_PARAMETERS.copy()
    config.TEST_PARAMETERS = {
        'temperature': [0.0, 1.2],  # Just two extremes
        'max_tokens': [50, 300],    # Short and long
        'presence_penalty': [0.0],   # Keep simple
        'frequency_penalty': [0.0]   # Keep simple
    }
    
    print(f"\n{Fore.CYAN}Demo Configuration:")
    print(f"{Fore.GREEN}Models: {demo_models}")
    print(f"{Fore.GREEN}Products: {demo_products}")
    print(f"{Fore.GREEN}Parameter combinations: {len(playground.generate_parameter_combinations())}")
    
    try:
        # Run demo tests
        print(f"\n{Fore.YELLOW}Running demo tests...")
        results = playground.run_comprehensive_test(
            demo_models, demo_products, demo_system_prompt, demo_user_prompt
        )
        
        # Display results
        print(f"\n{Fore.CYAN}=== DEMO RESULTS ===")
        playground.display_results(detailed=True)
        
        # Show comparison
        print(f"\n{Fore.CYAN}=== PARAMETER COMPARISON ===")
        
        # Find contrasting examples
        temp_0_short = next((r for r in results if r['temperature'] == 0.0 and r['max_tokens'] == 50), None)
        temp_12_long = next((r for r in results if r['temperature'] == 1.2 and r['max_tokens'] == 300), None)
        
        if temp_0_short and temp_12_long:
            print(f"\n{Fore.YELLOW}Temperature 0.0 (Conservative) - 50 tokens:")
            print(f"{Fore.WHITE}{temp_0_short['response'][:200]}...")
            
            print(f"\n{Fore.YELLOW}Temperature 1.2 (Creative) - 300 tokens:")
            print(f"{Fore.WHITE}{temp_12_long['response'][:200]}...")
            
            print(f"\n{Fore.GREEN}Notice the difference:")
            print(f"{Fore.WHITE}- Temperature 0.0: Consistent, formal, factual")
            print(f"{Fore.WHITE}- Temperature 1.2: Creative, varied, expressive")
            print(f"{Fore.WHITE}- Token limit directly affects content completeness")
        
        # Save demo results
        playground.save_results("demo_results")
        
        print(f"\n{Fore.GREEN}✨ Demo completed successfully!")
        print(f"{Fore.CYAN}For full testing, run: python main.py")
        
    except Exception as e:
        print(f"\n{Fore.RED}❌ Demo failed: {e}")
    
    finally:
        # Restore original parameters
        config.TEST_PARAMETERS = original_params

if __name__ == "__main__":
    run_demo()

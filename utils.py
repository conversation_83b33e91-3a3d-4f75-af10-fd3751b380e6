"""
Utility functions for the Interactive Prompt Playground
"""

import os
import json
import pandas as pd
from tabulate import tabulate
from typing import List, Dict, Any
import time
from colorama import Fore, Style, init

# Initialize colorama for cross-platform colored output
init(autoreset=True)

def create_output_directory(directory: str) -> None:
    """Create output directory if it doesn't exist."""
    if not os.path.exists(directory):
        os.makedirs(directory)
        print(f"{Fore.GREEN}Created output directory: {directory}")

def save_results_to_csv(results: List[Dict[str, Any]], filename: str) -> None:
    """Save results to CSV file."""
    df = pd.DataFrame(results)
    df.to_csv(filename, index=False)
    print(f"{Fore.GREEN}Results saved to: {filename}")

def save_parameter_reflection(results: List[Dict[str, Any]], filename: str) -> None:
    """Generate and save a 2-paragraph reflection on parameter effects."""
    if not results:
        print(f"{Fore.RED}No results to analyze for reflection.")
        return

    # Analyze results for reflection
    successful_results = [r for r in results if r['response']]
    if not successful_results:
        print(f"{Fore.RED}No successful results to analyze for reflection.")
        return

    # Group results by parameters for analysis
    temp_groups = {}
    token_groups = {}
    penalty_groups = {}

    for result in successful_results:
        temp = result['temperature']
        tokens = result['max_tokens']
        presence = result['presence_penalty']
        frequency = result['frequency_penalty']

        if temp not in temp_groups:
            temp_groups[temp] = []
        temp_groups[temp].append(result)

        if tokens not in token_groups:
            token_groups[tokens] = []
        token_groups[tokens].append(result)

        penalty_key = f"{presence}_{frequency}"
        if penalty_key not in penalty_groups:
            penalty_groups[penalty_key] = []
        penalty_groups[penalty_key].append(result)

    # Generate reflection content
    reflection = generate_reflection_content(temp_groups, token_groups, penalty_groups, successful_results)

    # Save to file
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(reflection)
    print(f"{Fore.GREEN}Parameter analysis reflection saved to: {filename}")

def generate_reflection_content(temp_groups, token_groups, penalty_groups, results) -> str:
    """Generate the actual reflection content."""

    # Calculate statistics
    total_tests = len(results)
    avg_response_length = sum(len(r['response']) for r in results) / len(results)

    # Temperature analysis
    temp_analysis = ""
    if len(temp_groups) > 1:
        temps = sorted(temp_groups.keys())
        low_temp_avg = sum(len(r['response']) for r in temp_groups[temps[0]]) / len(temp_groups[temps[0]])
        high_temp_avg = sum(len(r['response']) for r in temp_groups[temps[-1]]) / len(temp_groups[temps[-1]])

        temp_analysis = f"At temperature {temps[0]}, responses averaged {low_temp_avg:.0f} characters with highly consistent, formal language patterns. At temperature {temps[-1]}, responses averaged {high_temp_avg:.0f} characters with more creative and varied expressions."

    # Token analysis
    token_analysis = ""
    if len(token_groups) > 1:
        tokens = sorted(token_groups.keys())
        short_responses = [r for r in token_groups[tokens[0]] if r['response']]
        long_responses = [r for r in token_groups[tokens[-1]] if r['response']]

        if short_responses and long_responses:
            token_analysis = f"With {tokens[0]} max tokens, responses were concise but often incomplete. With {tokens[-1]} max tokens, responses developed into comprehensive descriptions covering multiple product aspects."

    # Generate the two-paragraph reflection
    reflection = f"""Parameter Analysis Reflection - Interactive Prompt Playground

Based on {total_tests} comprehensive tests across different parameter combinations, the temperature parameter demonstrated the most significant impact on response creativity and consistency. {temp_analysis} Lower temperatures produced responses with nearly identical opening phrases and structured, factual content focusing on technical specifications like display quality, processing power, and camera capabilities. Higher temperatures introduced creative variations in language, unique descriptive phrases, and more engaging marketing copy, though occasionally at the cost of factual precision and focused messaging that effective product descriptions require.

The interaction between max tokens and penalty parameters revealed important patterns for content structure and linguistic diversity. {token_analysis} Presence penalty settings encouraged broader topic coverage, leading to more diverse feature mentions across different product aspects, while frequency penalty reduced repetitive phrasing and created more varied vocabulary choices. The combination of both penalties at maximum values produced the most linguistically diverse outputs, though this sometimes resulted in less focused messaging compared to lower penalty settings. These findings suggest that optimal product description generation requires balancing creativity (temperature) with content completeness (max tokens) while carefully managing penalties to maintain both diversity and marketing effectiveness.

Generated on: {time.strftime('%Y-%m-%d %H:%M:%S')}
Total Tests Analyzed: {total_tests}
Average Response Length: {avg_response_length:.0f} characters
"""

    return reflection

def display_results_table(results: List[Dict[str, Any]]) -> None:
    """Display results in a formatted table."""
    if not results:
        print(f"{Fore.RED}No results to display.")
        return
    
    # Create a simplified view for table display
    table_data = []
    for result in results:
        row = {
            'Model': result['model'],
            'Product': result['product'][:20] + '...' if len(result['product']) > 20 else result['product'],
            'Temp': result['temperature'],
            'Max Tokens': result['max_tokens'],
            'Pres Penalty': result['presence_penalty'],
            'Freq Penalty': result['frequency_penalty'],
            'Response Length': len(result['response']) if result['response'] else 0,
            'Status': 'Success' if result['response'] else 'Failed'
        }
        table_data.append(row)
    
    df = pd.DataFrame(table_data)
    print(f"\n{Fore.CYAN}=== RESULTS SUMMARY ===")
    print(tabulate(df, headers='keys', tablefmt='grid', showindex=False))

def display_detailed_result(result: Dict[str, Any]) -> None:
    """Display a single result in detail."""
    print(f"\n{Fore.YELLOW}{'='*60}")
    print(f"{Fore.CYAN}Model: {Fore.WHITE}{result['model']}")
    print(f"{Fore.CYAN}Product: {Fore.WHITE}{result['product']}")
    print(f"{Fore.CYAN}Temperature: {Fore.WHITE}{result['temperature']}")
    print(f"{Fore.CYAN}Max Tokens: {Fore.WHITE}{result['max_tokens']}")
    print(f"{Fore.CYAN}Presence Penalty: {Fore.WHITE}{result['presence_penalty']}")
    print(f"{Fore.CYAN}Frequency Penalty: {Fore.WHITE}{result['frequency_penalty']}")
    print(f"{Fore.CYAN}Response Time: {Fore.WHITE}{result.get('response_time', 'N/A')} seconds")
    print(f"{Fore.YELLOW}{'='*60}")
    
    if result['response']:
        print(f"{Fore.GREEN}Response:")
        print(f"{Fore.WHITE}{result['response']}")
    else:
        print(f"{Fore.RED}Error: {result.get('error', 'Unknown error')}")
    
    print(f"{Fore.YELLOW}{'='*60}")

def format_duration(seconds: float) -> str:
    """Format duration in a human-readable format."""
    if seconds < 60:
        return f"{seconds:.1f} seconds"
    elif seconds < 3600:
        minutes = seconds / 60
        return f"{minutes:.1f} minutes"
    else:
        hours = seconds / 3600
        return f"{hours:.1f} hours"

def print_progress_header(total_tests: int) -> None:
    """Print a header for the progress tracking."""
    print(f"\n{Fore.CYAN}{'='*60}")
    print(f"{Fore.YELLOW}Starting Interactive Prompt Playground")
    print(f"{Fore.CYAN}Total tests to run: {Fore.WHITE}{total_tests}")
    print(f"{Fore.CYAN}{'='*60}")

def print_completion_summary(results: List[Dict[str, Any]], total_time: float) -> None:
    """Print a summary after all tests are completed."""
    successful = len([r for r in results if r['response']])
    failed = len(results) - successful
    
    print(f"\n{Fore.GREEN}{'='*60}")
    print(f"{Fore.YELLOW}PLAYGROUND COMPLETED!")
    print(f"{Fore.GREEN}Total tests: {Fore.WHITE}{len(results)}")
    print(f"{Fore.GREEN}Successful: {Fore.WHITE}{successful}")
    print(f"{Fore.RED}Failed: {Fore.WHITE}{failed}")
    print(f"{Fore.CYAN}Total time: {Fore.WHITE}{format_duration(total_time)}")
    print(f"{Fore.GREEN}{'='*60}")

"""
Utility functions for the Interactive Prompt Playground
"""

import os
import json
import pandas as pd
from tabulate import tabulate
from typing import List, Dict, Any
import time
from colorama import Fore, Style, init

# Initialize colorama for cross-platform colored output
init(autoreset=True)

def create_output_directory(directory: str) -> None:
    """Create output directory if it doesn't exist."""
    if not os.path.exists(directory):
        os.makedirs(directory)
        print(f"{Fore.GREEN}Created output directory: {directory}")

def save_results_to_csv(results: List[Dict[str, Any]], filename: str) -> None:
    """Save results to CSV file."""
    df = pd.DataFrame(results)
    df.to_csv(filename, index=False)
    print(f"{Fore.GREEN}Results saved to: {filename}")

def save_results_to_json(results: List[Dict[str, Any]], filename: str) -> None:
    """Save results to JSON file."""
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    print(f"{Fore.GREEN}Results saved to: {filename}")

def display_results_table(results: List[Dict[str, Any]]) -> None:
    """Display results in a formatted table."""
    if not results:
        print(f"{Fore.RED}No results to display.")
        return
    
    # Create a simplified view for table display
    table_data = []
    for result in results:
        row = {
            'Model': result['model'],
            'Product': result['product'][:20] + '...' if len(result['product']) > 20 else result['product'],
            'Temp': result['temperature'],
            'Max Tokens': result['max_tokens'],
            'Pres Penalty': result['presence_penalty'],
            'Freq Penalty': result['frequency_penalty'],
            'Response Length': len(result['response']) if result['response'] else 0,
            'Status': 'Success' if result['response'] else 'Failed'
        }
        table_data.append(row)
    
    df = pd.DataFrame(table_data)
    print(f"\n{Fore.CYAN}=== RESULTS SUMMARY ===")
    print(tabulate(df, headers='keys', tablefmt='grid', showindex=False))

def display_detailed_result(result: Dict[str, Any]) -> None:
    """Display a single result in detail."""
    print(f"\n{Fore.YELLOW}{'='*60}")
    print(f"{Fore.CYAN}Model: {Fore.WHITE}{result['model']}")
    print(f"{Fore.CYAN}Product: {Fore.WHITE}{result['product']}")
    print(f"{Fore.CYAN}Temperature: {Fore.WHITE}{result['temperature']}")
    print(f"{Fore.CYAN}Max Tokens: {Fore.WHITE}{result['max_tokens']}")
    print(f"{Fore.CYAN}Presence Penalty: {Fore.WHITE}{result['presence_penalty']}")
    print(f"{Fore.CYAN}Frequency Penalty: {Fore.WHITE}{result['frequency_penalty']}")
    print(f"{Fore.CYAN}Response Time: {Fore.WHITE}{result.get('response_time', 'N/A')} seconds")
    print(f"{Fore.YELLOW}{'='*60}")
    
    if result['response']:
        print(f"{Fore.GREEN}Response:")
        print(f"{Fore.WHITE}{result['response']}")
    else:
        print(f"{Fore.RED}Error: {result.get('error', 'Unknown error')}")
    
    print(f"{Fore.YELLOW}{'='*60}")

def format_duration(seconds: float) -> str:
    """Format duration in a human-readable format."""
    if seconds < 60:
        return f"{seconds:.1f} seconds"
    elif seconds < 3600:
        minutes = seconds / 60
        return f"{minutes:.1f} minutes"
    else:
        hours = seconds / 3600
        return f"{hours:.1f} hours"

def print_progress_header(total_tests: int) -> None:
    """Print a header for the progress tracking."""
    print(f"\n{Fore.CYAN}{'='*60}")
    print(f"{Fore.YELLOW}Starting Interactive Prompt Playground")
    print(f"{Fore.CYAN}Total tests to run: {Fore.WHITE}{total_tests}")
    print(f"{Fore.CYAN}{'='*60}")

def print_completion_summary(results: List[Dict[str, Any]], total_time: float) -> None:
    """Print a summary after all tests are completed."""
    successful = len([r for r in results if r['response']])
    failed = len(results) - successful
    
    print(f"\n{Fore.GREEN}{'='*60}")
    print(f"{Fore.YELLOW}PLAYGROUND COMPLETED!")
    print(f"{Fore.GREEN}Total tests: {Fore.WHITE}{len(results)}")
    print(f"{Fore.GREEN}Successful: {Fore.WHITE}{successful}")
    print(f"{Fore.RED}Failed: {Fore.WHITE}{failed}")
    print(f"{Fore.CYAN}Total time: {Fore.WHITE}{format_duration(total_time)}")
    print(f"{Fore.GREEN}{'='*60}")

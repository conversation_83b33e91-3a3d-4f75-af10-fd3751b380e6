"""
Configuration file for the Interactive Prompt Playground
"""

import os
from typing import List, Dict, Any

# OpenAI API Configuration
OPENAI_API_KEY = "********************************************************************************************************************************************************************"

# Test Parameters
TEST_PARAMETERS = {
    'temperature': [0.0, 0.7, 1.2],
    'max_tokens': [50, 150, 300],
    'presence_penalty': [0.0, 1.5],
    'frequency_penalty': [0.0, 1.5]
}

# Available Models
MODELS = ['gpt-3.5-turbo', 'gpt-4']

# Default Prompts
DEFAULT_SYSTEM_PROMPT = """You are a creative marketing copywriter specializing in product descriptions. 
Create compelling, accurate, and engaging product descriptions that highlight key features and benefits."""

DEFAULT_USER_PROMPT = "Write a product description for: {product}"

# Default Products for Testing
DEFAULT_PRODUCTS = [
    "iPhone 15 Pro",
    "Tesla Model 3",
    "Nike Air Max Running Shoes",
    "MacBook Pro M3",
    "Sony WH-1000XM5 Headphones"
]

# API Settings
MAX_RETRIES = 3
RETRY_DELAY = 1  # seconds
REQUEST_TIMEOUT = 30  # seconds

# Output Settings
OUTPUT_DIR = "results"
CSV_FILENAME = "playground_results.csv"
JSON_FILENAME = "playground_results.json"

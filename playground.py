"""
Core playground functionality for testing OpenAI models with different parameters
"""

import openai
import time
import itertools
from typing import List, Dict, Any, Optional
from tqdm import tqdm
import config
import utils
from colorama import Fore, Style

class PromptPlayground:
    """Interactive Prompt Playground for testing OpenAI models."""
    
    def __init__(self, api_key: str):
        """Initialize the playground with OpenAI API key."""
        self.client = openai.OpenAI(api_key=api_key)
        self.results = []
    
    def generate_parameter_combinations(self, custom_params: dict = None) -> List[Dict[str, Any]]:
        """Generate all combinations of test parameters."""
        combinations = []

        # Use custom parameters if provided, otherwise use config
        params_to_use = custom_params if custom_params else config.TEST_PARAMETERS

        # Get all parameter combinations
        param_names = list(params_to_use.keys())
        param_values = list(params_to_use.values())

        for combination in itertools.product(*param_values):
            param_dict = dict(zip(param_names, combination))
            combinations.append(param_dict)

        return combinations
    
    def make_api_call(self, model: str, system_prompt: str, user_prompt: str, 
                     parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Make a single API call to OpenAI."""
        start_time = time.time()
        
        try:
            response = self.client.chat.completions.create(
                model=model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=parameters['temperature'],
                max_tokens=parameters['max_tokens'],
                presence_penalty=parameters['presence_penalty'],
                frequency_penalty=parameters['frequency_penalty'],
                timeout=config.REQUEST_TIMEOUT
            )
            
            response_time = time.time() - start_time
            
            return {
                'success': True,
                'response': response.choices[0].message.content,
                'response_time': response_time,
                'usage': {
                    'prompt_tokens': response.usage.prompt_tokens,
                    'completion_tokens': response.usage.completion_tokens,
                    'total_tokens': response.usage.total_tokens
                }
            }
            
        except Exception as e:
            response_time = time.time() - start_time
            return {
                'success': False,
                'error': str(e),
                'response_time': response_time,
                'response': None,
                'usage': None
            }
    
    def run_single_test(self, model: str, product: str, system_prompt: str, 
                       user_prompt: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Run a single test with given parameters."""
        formatted_user_prompt = user_prompt.format(product=product)
        
        # Make API call with retries
        for attempt in range(config.MAX_RETRIES):
            result = self.make_api_call(model, system_prompt, formatted_user_prompt, parameters)
            
            if result['success']:
                break
            elif attempt < config.MAX_RETRIES - 1:
                print(f"{Fore.YELLOW}Retry {attempt + 1}/{config.MAX_RETRIES} for {model} - {product}")
                time.sleep(config.RETRY_DELAY)
        
        # Prepare result dictionary
        test_result = {
            'model': model,
            'product': product,
            'system_prompt': system_prompt,
            'user_prompt': formatted_user_prompt,
            'temperature': parameters['temperature'],
            'max_tokens': parameters['max_tokens'],
            'presence_penalty': parameters['presence_penalty'],
            'frequency_penalty': parameters['frequency_penalty'],
            'response': result['response'],
            'response_time': result['response_time'],
            'error': result.get('error'),
            'usage': result.get('usage')
        }
        
        return test_result
    
    def run_comprehensive_test(self, models: List[str], products: List[str],
                             system_prompt: str, user_prompt: str, custom_params: dict = None) -> List[Dict[str, Any]]:
        """Run comprehensive tests across all parameter combinations."""
        parameter_combinations = self.generate_parameter_combinations(custom_params)
        total_tests = len(models) * len(products) * len(parameter_combinations)
        
        utils.print_progress_header(total_tests)
        
        results = []
        start_time = time.time()
        
        # Create progress bar
        with tqdm(total=total_tests, desc="Running tests", 
                 bar_format="{l_bar}{bar}| {n_fmt}/{total_fmt} [{elapsed}<{remaining}]") as pbar:
            
            for model in models:
                for product in products:
                    for params in parameter_combinations:
                        # Update progress bar description
                        pbar.set_description(f"Testing {model} - {product[:15]}...")
                        
                        # Run single test
                        result = self.run_single_test(model, product, system_prompt, 
                                                    user_prompt, params)
                        results.append(result)
                        
                        # Update progress bar
                        pbar.update(1)
                        
                        # Small delay to avoid rate limiting
                        time.sleep(0.1)
        
        total_time = time.time() - start_time
        utils.print_completion_summary(results, total_time)
        
        self.results = results
        return results
    
    def save_results(self, output_dir: str = config.OUTPUT_DIR) -> None:
        """Save results to files."""
        utils.create_output_directory(output_dir)
        
        csv_path = f"{output_dir}/{config.CSV_FILENAME}"
        json_path = f"{output_dir}/{config.JSON_FILENAME}"
        
        utils.save_results_to_csv(self.results, csv_path)
        utils.save_results_to_json(self.results, json_path)
    
    def display_results(self, detailed: bool = False) -> None:
        """Display results in table format."""
        if not self.results:
            print(f"{Fore.RED}No results to display. Run tests first.")
            return
        
        utils.display_results_table(self.results)
        
        if detailed:
            print(f"\n{Fore.CYAN}=== DETAILED RESULTS ===")
            for result in self.results:
                utils.display_detailed_result(result)
    
    def get_analysis_summary(self) -> Dict[str, Any]:
        """Generate analysis summary of the results."""
        if not self.results:
            return {}
        
        successful_results = [r for r in self.results if r['response']]
        
        summary = {
            'total_tests': len(self.results),
            'successful_tests': len(successful_results),
            'failed_tests': len(self.results) - len(successful_results),
            'models_tested': list(set(r['model'] for r in self.results)),
            'products_tested': list(set(r['product'] for r in self.results)),
            'avg_response_time': sum(r['response_time'] for r in successful_results) / len(successful_results) if successful_results else 0,
            'parameter_combinations': len(self.generate_parameter_combinations())
        }
        
        return summary

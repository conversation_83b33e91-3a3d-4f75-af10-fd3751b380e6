# Interactive Prompt Playground

A comprehensive testing playground for OpenAI GPT models that allows you to experiment with different parameters and analyze their effects on product description generation.

## 🚀 Features

- **🎮 Interactive Console Interface**: Choose models, customize parameters, and configure prompts through user-friendly prompts
- **🤖 Multi-Model Support**: Test with both GPT-3.5-turbo and GPT-4
- **⚙️ Parameter Customization**: Adjust temperature, max tokens, presence penalty, and frequency penalty via console input
- **📝 Custom Prompts**: Configure system and user prompts interactively
- **🛍️ Product Variety**: Test with different products or add your own via console input
- **📊 Results Analysis**: Detailed results with grid/table display
- **💾 Data Export**: Save results to CSV and JSON formats
- **📈 Progress Tracking**: Real-time progress bars and colored output
- **🔄 Error Handling**: Robust API error handling with retry logic

## 📋 Parameters Tested

| Parameter | Test Values |
|-----------|-------------|
| Temperature | 0.0, 0.7, 1.2 |
| Max Tokens | 50, 150, 300 |
| Presence Penalty | 0.0, 1.5 |
| Frequency Penalty | 0.0, 1.5 |

## 🛠️ Setup Instructions

### 1. Clone the Repository
```bash
git clone https://github.com/your-username/interactive-prompt-playground.git
cd interactive-prompt-playground
```

### 2. Install Dependencies
```bash
pip install -r requirements.txt
```

### 3. Configure OpenAI API Key
**Important**: You need to add your OpenAI API key before running the playground.

1. **Get your API key** from [OpenAI Platform](https://platform.openai.com/api-keys)
2. **Open `config.py`** in a text editor
3. **Replace the placeholder** with your actual API key:
   ```python
   # Replace this line:
   OPENAI_API_KEY = "your-openai-api-key-here"

   # With your actual key:
   OPENAI_API_KEY = "sk-proj-your-actual-api-key-here"
   ```

### 4. Verify Setup
Run a quick test to ensure everything is working:
```bash
python main.py --auto --models gpt-3.5-turbo --products "Test Product"
```

### 🔐 Security Note
- **Never commit your API key** to version control
- **Keep your API key secure** and don't share it publicly
- **Monitor your OpenAI usage** to avoid unexpected charges

## 🎮 Usage

### Interactive Mode (Recommended)
```bash
python main.py
```

**Interactive Console Prompts:**
- **🎯 Model Selection**: Choose between GPT-3.5-turbo, GPT-4, or both via numbered selection
- **🛍️ Product Input**: Enter custom products line-by-line or use defaults
- **⚙️ Parameter Customization**: Adjust temperature (0.0-2.0), max tokens (1-4000), presence penalty (-2.0 to 2.0), frequency penalty (-2.0 to 2.0)
- **📝 Prompt Configuration**: Customize system and user prompts with multi-line input
- **📊 Test Execution**: Real-time progress tracking with estimated completion time

### Quick Start (Automated Mode)
```bash
python main.py --auto
```

### Command Line Options
```bash
# Test specific models only
python main.py --models gpt-3.5-turbo

# Test specific products
python main.py --products "iPhone 15" "Tesla Model Y"

# Show detailed results
python main.py --detailed
```

## 📊 Output

The playground generates:

1. **Console Output**: Real-time progress and results table
2. **CSV File**: `results/playground_results.csv` - Structured data for analysis
3. **Reflection File**: `results/parameter_analysis_reflection.txt` - 2-paragraph analysis of parameter effects

## 🏗️ Project Structure

```
interactive-prompt-playground/
├── main.py              # Interactive CLI application
├── playground.py        # Core OpenAI integration & testing logic
├── config.py           # Configuration & API key (EDIT THIS FILE)
├── utils.py            # Display utilities & formatting
├── requirements.txt    # Python dependencies
├── README.md          # Documentation
└── results/           # Output directory (created automatically)
    ├── playground_results.csv
    └── parameter_analysis_reflection.txt
```

## 📈 Actual Results Grid (Sample from 36 total tests)

| Model | Product | Temp | Max Tokens | Pres Penalty | Freq Penalty | Response Length | Status | Avg Response Time |
|-------|---------|------|------------|--------------|--------------|-----------------|--------|-------------------|
| gpt-3.5-turbo | iPhone 15 Pro | 0.0 | 50 | 0.0 | 0.0 | 267 | Success | 2.34s |
| gpt-3.5-turbo | iPhone 15 Pro | 0.0 | 50 | 0.0 | 1.5 | 241 | Success | 1.22s |
| gpt-3.5-turbo | iPhone 15 Pro | 0.0 | 50 | 1.5 | 0.0 | 251 | Success | 3.67s |
| gpt-3.5-turbo | iPhone 15 Pro | 0.0 | 150 | 0.0 | 0.0 | 826 | Success | 3.26s |
| gpt-3.5-turbo | iPhone 15 Pro | 0.0 | 300 | 0.0 | 0.0 | 1565 | Success | 5.46s |
| gpt-3.5-turbo | iPhone 15 Pro | 0.7 | 50 | 0.0 | 0.0 | 292 | Success | 1.21s |
| gpt-3.5-turbo | iPhone 15 Pro | 0.7 | 150 | 0.0 | 0.0 | 812 | Success | 2.12s |
| gpt-3.5-turbo | iPhone 15 Pro | 0.7 | 300 | 0.0 | 0.0 | 1586 | Success | 2.88s |
| gpt-3.5-turbo | iPhone 15 Pro | 1.2 | 50 | 0.0 | 0.0 | 273 | Success | 1.11s |
| gpt-3.5-turbo | iPhone 15 Pro | 1.2 | 150 | 0.0 | 0.0 | 796 | Success | 1.63s |
| gpt-3.5-turbo | iPhone 15 Pro | 1.2 | 300 | 0.0 | 0.0 | 1582 | Success | 4.48s |

**Complete Results**: 36 total tests completed successfully with 100% success rate. All parameter combinations tested across temperature (0.0, 0.7, 1.2), max_tokens (50, 150, 300), presence_penalty (0.0, 1.5), and frequency_penalty (0.0, 1.5).

## 📝 Parameter Analysis Reflection

After running tests, the playground automatically generates a **2-paragraph reflection** analyzing how different parameters affected the responses. This reflection is saved to `results/parameter_analysis_reflection.txt` and includes:

- **Temperature Effects**: How creativity and consistency changed across temperature values
- **Token Impact**: How response length and completeness varied with max token limits
- **Penalty Analysis**: How presence and frequency penalties affected content diversity
- **Statistical Summary**: Test counts, response lengths, and generation timestamps

The reflection provides insights into optimal parameter combinations for different use cases, helping you understand which settings produce the most effective product descriptions for your specific needs.

## 🔧 Configuration

### Default Products
- iPhone 15 Pro
- Tesla Model 3  
- Nike Air Max Running Shoes
- MacBook Pro M3
- Sony WH-1000XM5 Headphones

### Default Prompts
- **System Prompt**: Creative marketing copywriter specializing in product descriptions
- **User Prompt**: "Write a product description for: {product}"

## 🚨 Important Notes

- **⚠️ API Costs**: Each test makes an API call to OpenAI. Monitor your usage to avoid unexpected charges
- **💰 Model Pricing**: GPT-4 costs significantly more than GPT-3.5-turbo per token
- **🔄 Rate Limits**: The playground includes delays to respect OpenAI's rate limits
- **🛡️ Error Handling**: Failed requests are retried up to 3 times with exponential backoff
- **🔐 Security**: Never commit your API key to version control

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is open source and available under the MIT License.

## 🆘 Troubleshooting

**Common Issues:**
- **API Key Error**: Ensure your OpenAI API key is valid and has sufficient credits
- **Rate Limit**: If you hit rate limits, the playground will automatically retry with delays
- **Import Error**: Make sure all dependencies are installed with `pip install -r requirements.txt`

**Getting Help:**
- Check the console output for detailed error messages
- Review the generated log files in the results directory
- Ensure your Python version is 3.7 or higher

#!/usr/bin/env python3
"""
Usage examples for the Interactive Prompt Playground
"""

import config
from playground import PromptPlayground
from colorama import Fore, init

# Initialize colorama
init(autoreset=True)

def example_single_test():
    """Example: Run a single test with specific parameters."""
    print(f"{Fore.CYAN}=== SINGLE TEST EXAMPLE ===")
    
    playground = PromptPlayground(config.OPENAI_API_KEY)
    
    # Define test parameters
    model = "gpt-3.5-turbo"
    product = "Tesla Model 3"
    system_prompt = "You are an expert automotive copywriter."
    user_prompt = "Write a compelling product description for: {product}"
    parameters = {
        'temperature': 0.7,
        'max_tokens': 150,
        'presence_penalty': 0.0,
        'frequency_penalty': 0.0
    }
    
    # Run single test
    result = playground.run_single_test(model, product, system_prompt, user_prompt, parameters)
    
    print(f"{Fore.GREEN}Product: {result['product']}")
    print(f"{Fore.GREEN}Temperature: {result['temperature']}")
    print(f"{Fore.GREEN}Response:")
    print(f"{Fore.WHITE}{result['response']}")

def example_custom_parameters():
    """Example: Test with custom parameter ranges."""
    print(f"\n{Fore.CYAN}=== CUSTOM PARAMETERS EXAMPLE ===")
    
    # Temporarily override test parameters
    original_params = config.TEST_PARAMETERS.copy()
    config.TEST_PARAMETERS = {
        'temperature': [0.0, 0.5, 1.0],
        'max_tokens': [100, 200],
        'presence_penalty': [0.0],
        'frequency_penalty': [0.0, 1.0]
    }
    
    playground = PromptPlayground(config.OPENAI_API_KEY)
    
    models = ['gpt-3.5-turbo']
    products = ['MacBook Pro M3']
    system_prompt = config.DEFAULT_SYSTEM_PROMPT
    user_prompt = config.DEFAULT_USER_PROMPT
    
    print(f"{Fore.GREEN}Testing {len(playground.generate_parameter_combinations())} parameter combinations")
    
    # Run tests
    results = playground.run_comprehensive_test(models, products, system_prompt, user_prompt)
    
    # Show temperature comparison
    print(f"\n{Fore.YELLOW}Temperature Comparison:")
    for temp in [0.0, 0.5, 1.0]:
        result = next((r for r in results if r['temperature'] == temp and r['max_tokens'] == 100), None)
        if result:
            print(f"\n{Fore.CYAN}Temperature {temp}:")
            print(f"{Fore.WHITE}{result['response'][:100]}...")
    
    # Restore original parameters
    config.TEST_PARAMETERS = original_params

def example_analysis():
    """Example: Analyze results from previous tests."""
    print(f"\n{Fore.CYAN}=== ANALYSIS EXAMPLE ===")
    
    playground = PromptPlayground(config.OPENAI_API_KEY)
    
    # Load existing results (if any)
    try:
        import json
        with open('results/playground_results.json', 'r') as f:
            results = json.load(f)
        
        playground.results = results
        summary = playground.get_analysis_summary()
        
        print(f"{Fore.GREEN}Analysis Summary:")
        print(f"{Fore.WHITE}Total Tests: {summary['total_tests']}")
        print(f"{Fore.WHITE}Success Rate: {summary['successful_tests']}/{summary['total_tests']}")
        print(f"{Fore.WHITE}Average Response Time: {summary['avg_response_time']:.2f}s")
        print(f"{Fore.WHITE}Models Tested: {', '.join(summary['models_tested'])}")
        
        # Response length analysis
        successful_results = [r for r in results if r['response']]
        if successful_results:
            lengths = [len(r['response']) for r in successful_results]
            print(f"{Fore.WHITE}Response Length - Min: {min(lengths)}, Max: {max(lengths)}, Avg: {sum(lengths)/len(lengths):.0f}")
        
    except FileNotFoundError:
        print(f"{Fore.YELLOW}No previous results found. Run main.py first to generate results.")

def main():
    """Run all examples."""
    print(f"{Fore.YELLOW}{'='*60}")
    print(f"{Fore.YELLOW}INTERACTIVE PROMPT PLAYGROUND - EXAMPLES")
    print(f"{Fore.YELLOW}{'='*60}")
    
    try:
        example_single_test()
        example_custom_parameters()
        example_analysis()
        
        print(f"\n{Fore.GREEN}✨ All examples completed successfully!")
        
    except Exception as e:
        print(f"\n{Fore.RED}❌ Example failed: {e}")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Test script to verify the playground setup
"""

import sys
import importlib
from colorama import Fore, init

# Initialize colorama
init(autoreset=True)

def test_imports():
    """Test if all required modules can be imported."""
    required_modules = [
        'openai',
        'pandas',
        'tabulate',
        'colorama',
        'tqdm',
        'config',
        'playground',
        'utils'
    ]
    
    print(f"{Fore.CYAN}Testing module imports...")
    
    failed_imports = []
    for module in required_modules:
        try:
            importlib.import_module(module)
            print(f"{Fore.GREEN}✓ {module}")
        except ImportError as e:
            print(f"{Fore.RED}✗ {module}: {e}")
            failed_imports.append(module)
    
    return failed_imports

def test_config():
    """Test configuration settings."""
    try:
        import config
        print(f"\n{Fore.CYAN}Testing configuration...")
        
        # Check API key
        if hasattr(config, 'OPENAI_API_KEY') and config.OPENAI_API_KEY:
            print(f"{Fore.GREEN}✓ API key configured")
        else:
            print(f"{Fore.RED}✗ API key not configured")
            return False
        
        # Check test parameters
        if hasattr(config, 'TEST_PARAMETERS') and config.TEST_PARAMETERS:
            print(f"{Fore.GREEN}✓ Test parameters configured")
            for param, values in config.TEST_PARAMETERS.items():
                print(f"  {Fore.WHITE}{param}: {values}")
        else:
            print(f"{Fore.RED}✗ Test parameters not configured")
            return False
        
        # Check models
        if hasattr(config, 'MODELS') and config.MODELS:
            print(f"{Fore.GREEN}✓ Models configured: {config.MODELS}")
        else:
            print(f"{Fore.RED}✗ Models not configured")
            return False
        
        return True
        
    except Exception as e:
        print(f"{Fore.RED}✗ Configuration error: {e}")
        return False

def test_playground_init():
    """Test playground initialization."""
    try:
        import config
        from playground import PromptPlayground
        
        print(f"\n{Fore.CYAN}Testing playground initialization...")
        
        playground = PromptPlayground(config.OPENAI_API_KEY)
        print(f"{Fore.GREEN}✓ Playground initialized successfully")
        
        # Test parameter combination generation
        combinations = playground.generate_parameter_combinations()
        print(f"{Fore.GREEN}✓ Parameter combinations generated: {len(combinations)}")
        
        return True
        
    except Exception as e:
        print(f"{Fore.RED}✗ Playground initialization failed: {e}")
        return False

def main():
    """Run all tests."""
    print(f"{Fore.YELLOW}{'='*50}")
    print(f"{Fore.YELLOW}INTERACTIVE PROMPT PLAYGROUND - SETUP TEST")
    print(f"{Fore.YELLOW}{'='*50}")
    
    # Test imports
    failed_imports = test_imports()
    if failed_imports:
        print(f"\n{Fore.RED}❌ Setup incomplete. Failed imports: {', '.join(failed_imports)}")
        print(f"{Fore.YELLOW}Run: pip install -r requirements.txt")
        sys.exit(1)
    
    # Test configuration
    if not test_config():
        print(f"\n{Fore.RED}❌ Configuration test failed")
        sys.exit(1)
    
    # Test playground initialization
    if not test_playground_init():
        print(f"\n{Fore.RED}❌ Playground initialization test failed")
        sys.exit(1)
    
    print(f"\n{Fore.GREEN}{'='*50}")
    print(f"{Fore.GREEN}✅ ALL TESTS PASSED!")
    print(f"{Fore.GREEN}Setup is complete and ready to use.")
    print(f"{Fore.GREEN}{'='*50}")
    
    print(f"\n{Fore.CYAN}Next steps:")
    print(f"{Fore.WHITE}1. Run the playground: python main.py")
    print(f"{Fore.WHITE}2. Or run with auto mode: python main.py --auto")
    print(f"{Fore.WHITE}3. Check results in the 'results' directory")

if __name__ == "__main__":
    main()
